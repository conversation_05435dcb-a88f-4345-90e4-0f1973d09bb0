# TRA CỨU CHI TIẾT: runserver.py

## 📍 VỊ TRÍ FILE
**Đường dẫn:** `django/core/management/commands/runserver.py`
**Mục đích:** Lệnh khởi động server phát triển Django

---

## 🔍 PHÂN TÍCH TỪNG ĐOẠN CODE

### 1. **IMPORT VÀ DEPENDENCIES (Dòng 1-14)**
```python
import errno
import os
import re
import socket
import sys
from datetime import datetime

from django.conf import settings
from django.core.management.base import BaseCommand, CommandError
from django.core.servers.basehttp import WSGIServer, get_internal_wsgi_application, run
from django.db import connections
from django.utils import autoreload
from django.utils.regex_helper import _lazy_re_compile
from django.utils.version import get_docs_version
```

**🎯 TÁC DỤNG:**
- `errno`: Xử lý mã lỗi hệ thống (port đã sử dụng, không có quyền truy cập)
- `socket`: <PERSON><PERSON>m tra hỗ trợ IPv6, xử lý network
- `BaseCommand`: Class cha cho tất cả Django management commands
- `WSGIServer`: Server WSGI để chạy ứng dụng Django
- `autoreload`: Tự động restart server khi file thay đổi

**🚀 KHI NÀO DÙNG:**
- Khi cần hiểu cách Django khởi động server
- Khi muốn tùy chỉnh server behavior
- Khi debug lỗi server không khởi động được

---

### 2. **REGEX PATTERN CHO IP:PORT (Dòng 16-24)**
```python
naiveip_re = _lazy_re_compile(
    r"""^(?:
(?P<addr>
    (?P<ipv4>\d{1,3}(?:\.\d{1,3}){3}) |         # IPv4 address
    (?P<ipv6>\[[a-fA-F0-9:]+\]) |               # IPv6 address
    (?P<fqdn>[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*) # FQDN
):)?(?P<port>\d+)$""",
    re.X,
)
```

**🎯 TÁC DỤNG:**
- Validate và parse địa chỉ IP:port từ command line
- Hỗ trợ IPv4: `127.0.0.1:8000`
- Hỗ trợ IPv6: `[::1]:8000`
- Hỗ trợ domain: `localhost:8000`
- Chỉ port: `8080`

**🚀 KHI NÀO DÙNG:**
- Khi cần validate format IP:port trong ứng dụng
- Khi tạo custom command cần parse network address
- Khi debug lỗi "invalid port number or address:port pair"

---

### 3. **CLASS COMMAND DEFINITION (Dòng 27-37)**
```python
class Command(BaseCommand):
    help = "Starts a lightweight web server for development."

    stealth_options = ("shutdown_message",)
    suppressed_base_arguments = {"--verbosity", "--traceback"}

    default_addr = "127.0.0.1"
    default_addr_ipv6 = "::1"
    default_port = "8000"
    protocol = "http"
    server_cls = WSGIServer
```

**🎯 TÁC DỤNG:**
- `help`: Text hiển thị khi chạy `python manage.py help runserver`
- `stealth_options`: Options không hiển thị trong help
- `suppressed_base_arguments`: Ẩn arguments từ BaseCommand
- `default_addr`: IP mặc định cho IPv4
- `default_port`: Port mặc định (8000)
- `server_cls`: Class server sử dụng (có thể override)

**🚀 KHI NÀO DÙNG:**
- Khi tạo custom management command
- Khi cần thay đổi server class (ví dụ: ASGI server)
- Khi muốn hiểu cấu trúc Django command

---

### 4. **ADD_ARGUMENTS METHOD (Dòng 39-61)**
```python
def add_arguments(self, parser):
    parser.add_argument(
        "addrport", nargs="?", help="Optional port number, or ipaddr:port"
    )
    parser.add_argument(
        "--ipv6", "-6",
        action="store_true",
        dest="use_ipv6",
        help="Tells Django to use an IPv6 address.",
    )
    parser.add_argument(
        "--nothreading",
        action="store_false",
        dest="use_threading",
        help="Tells Django to NOT use threading.",
    )
    parser.add_argument(
        "--noreload",
        action="store_false",
        dest="use_reloader",
        help="Tells Django to NOT use the auto-reloader.",
    )
```

**🎯 TÁC DỤNG:**
- `addrport`: Argument positional cho IP:port (optional)
- `--ipv6`: Flag để sử dụng IPv6
- `--nothreading`: Tắt threading (single-threaded server)
- `--noreload`: Tắt auto-reload khi file thay đổi

**🚀 KHI NÀO DÙNG:**
- `--noreload`: Khi debug auto-reload gây vấn đề
- `--nothreading`: Khi debug threading issues
- `--ipv6`: Khi test IPv6 connectivity
- `addrport`: Khi cần chạy trên port/IP khác

**📝 VÍ DỤ SỬ DỤNG:**
```bash
python manage.py runserver                    # 127.0.0.1:8000
python manage.py runserver 8080               # 127.0.0.1:8080
python manage.py runserver 0.0.0.0:8000      # Tất cả interfaces
python manage.py runserver --noreload         # Không auto-reload
python manage.py runserver --ipv6             # IPv6
```

---

### 5. **EXECUTE METHOD (Dòng 63-69)**
```python
def execute(self, *args, **options):
    if options["no_color"]:
        # We rely on the environment because it's currently the only
        # way to reach WSGIRequestHandler. This seems an acceptable
        # compromise considering `runserver` runs indefinitely.
        os.environ["DJANGO_COLORS"] = "nocolor"
    super().execute(*args, **options)
```

**🎯 TÁC DỤNG:**
- Xử lý option `--no-color` để tắt màu sắc trong output
- Set environment variable `DJANGO_COLORS=nocolor`
- Gọi execute method của BaseCommand

**🚀 KHI NÀO DÙNG:**
- Khi chạy trong CI/CD environment cần plain text
- Khi terminal không hỗ trợ màu sắc
- Khi redirect output vào file

---

### 6. **GET_HANDLER METHOD (Dòng 71-73)**
```python
def get_handler(self, *args, **options):
    """Return the default WSGI handler for the runner."""
    return get_internal_wsgi_application()
```

**🎯 TÁC DỤNG:**
- Trả về WSGI application handler
- `get_internal_wsgi_application()` load WSGI app từ settings

**🚀 KHI NÀO DÙNG:**
- Khi cần override WSGI handler
- Khi tạo custom server command
- Khi debug WSGI application loading

---

### 7. **HANDLE METHOD - VALIDATION (Dòng 79-110)**
```python
def handle(self, *args, **options):
    if not settings.DEBUG and not settings.ALLOWED_HOSTS:
        raise CommandError("You must set settings.ALLOWED_HOSTS if DEBUG is False.")

    self.use_ipv6 = options["use_ipv6"]
    if self.use_ipv6 and not socket.has_ipv6:
        raise CommandError("Your Python does not support IPv6.")
```

**🎯 TÁC DỤNG:**
- **Security check:** Bắt buộc set ALLOWED_HOSTS khi DEBUG=False
- **IPv6 check:** Kiểm tra Python có hỗ trợ IPv6 không
- Ngăn chặn lỗi bảo mật phổ biến

**🚀 KHI NÀO DÙNG:**
- Khi deploy production mà quên set ALLOWED_HOSTS
- Khi gặp lỗi "You must set settings.ALLOWED_HOSTS"
- Khi test IPv6 trên hệ thống không hỗ trợ

---

### 8. **ADDRESS PARSING (Dòng 87-110)**
```python
if not options["addrport"]:
    self.addr = ""
    self.port = self.default_port
else:
    m = re.match(naiveip_re, options["addrport"])
    if m is None:
        raise CommandError(
            '"%s" is not a valid port number '
            "or address:port pair." % options["addrport"]
        )
    self.addr, _ipv4, _ipv6, _fqdn, self.port = m.groups()
    if not self.port.isdigit():
        raise CommandError("%r is not a valid port number." % self.port)
```

**🎯 TÁC DỤNG:**
- Parse và validate địa chỉ IP:port từ command line
- Xử lý các format: `8000`, `127.0.0.1:8000`, `[::1]:8000`
- Validate port number phải là số

**🚀 KHI NÀO DÙNG:**
- Khi gặp lỗi "not a valid port number"
- Khi cần hiểu cách Django parse network address
- Khi tạo tool parse IP:port tương tự

---

### 9. **RUN METHOD - AUTO-RELOAD (Dòng 112-119)**
```python
def run(self, **options):
    """Run the server, using the autoreloader if needed."""
    use_reloader = options["use_reloader"]

    if use_reloader:
        autoreload.run_with_reloader(self.inner_run, **options)
    else:
        self.inner_run(None, **options)
```

**🎯 TÁC DỤNG:**
- Quyết định có sử dụng auto-reloader hay không
- `autoreload.run_with_reloader()`: Restart server khi file thay đổi
- `inner_run()`: Chạy server bình thường

**🚀 KHI NÀO DÙNG:**
- Khi auto-reload gây vấn đề (dùng `--noreload`)
- Khi muốn hiểu cơ chế auto-reload của Django
- Khi tạo custom server với auto-reload

---

### 10. **INNER_RUN - SYSTEM CHECKS (Dòng 130-140)**
```python
if not options["skip_checks"]:
    self.stdout.write("Performing system checks...\n\n")
    check_kwargs = super().get_check_kwargs(options)
    check_kwargs["display_num_errors"] = True
    self.check(**check_kwargs)
# Need to check migrations here, so can't use the
# requires_migrations_check attribute.
self.check_migrations()
# Close all connections opened during migration checking.
for conn in connections.all(initialized_only=True):
    conn.close()
```

**🎯 TÁC DỤNG:**
- Chạy system checks trước khi start server
- Kiểm tra migrations chưa apply
- Đóng database connections sau check
- Hiển thị "Performing system checks..." message

**🚀 KHI NÀO DÙNG:**
- Khi muốn skip system checks (dùng `--skip-checks`)
- Khi debug migration warnings
- Khi hiểu quy trình startup của Django

---

### 11. **ERROR HANDLING (Dòng 153-170)**
```python
except OSError as e:
    # Use helpful error messages instead of ugly tracebacks.
    ERRORS = {
        errno.EACCES: "You don't have permission to access that port.",
        errno.EADDRINUSE: "That port is already in use.",
        errno.EADDRNOTAVAIL: "That IP address can't be assigned to.",
    }
    try:
        error_text = ERRORS[e.errno]
    except KeyError:
        error_text = e
    self.stderr.write("Error: %s" % error_text)
    # Need to use an OS exit because sys.exit doesn't work in a thread
    os._exit(1)
```

**🎯 TÁC DỤNG:**
- Chuyển đổi OS errors thành human-readable messages
- `EACCES`: Không có quyền truy cập port (thường port < 1024)
- `EADDRINUSE`: Port đã được sử dụng bởi process khác
- `EADDRNOTAVAIL`: IP address không hợp lệ

**🚀 KHI NÀO DÙNG:**
- Khi gặp lỗi "That port is already in use" → kill process đang dùng port
- Khi gặp lỗi "You don't have permission" → dùng sudo hoặc port > 1024
- Khi debug network connectivity issues

---

## 🎯 TỔNG KẾT CÁCH SỬ DỤNG

### ✅ **Các trường hợp sử dụng phổ biến:**

1. **Development bình thường:**
   ```bash
   python manage.py runserver
   ```

2. **Chạy trên port khác:**
   ```bash
   python manage.py runserver 8080
   ```

3. **Cho phép truy cập từ mạng ngoài:**
   ```bash
   python manage.py runserver 0.0.0.0:8000
   ```

4. **Tắt auto-reload (khi có vấn đề):**
   ```bash
   python manage.py runserver --noreload
   ```

5. **Production-like testing:**
   ```bash
   python manage.py runserver --nothreading
   ```

### 🚨 **Các lỗi thường gặp và cách xử lý:**

1. **"That port is already in use"**
   - Tìm process: `lsof -i :8000` (macOS/Linux)
   - Kill process: `kill -9 <PID>`
   - Hoặc dùng port khác: `python manage.py runserver 8001`

2. **"You must set settings.ALLOWED_HOSTS"**
   - Thêm vào settings.py: `ALLOWED_HOSTS = ['localhost', '127.0.0.1']`

3. **"You don't have permission to access that port"**
   - Dùng port > 1024: `python manage.py runserver 8000`
   - Hoặc chạy với sudo (không khuyến nghị)

### 🔧 **Customization tips:**

1. **Tạo custom server command:**
   - Inherit từ `Command` class này
   - Override `server_cls` để dùng server khác
   - Override `get_handler()` để custom WSGI app

2. **Environment variables:**
   - `DJANGO_RUNSERVER_HIDE_WARNING=true`: Ẩn warning message
   - `DJANGO_COLORS=nocolor`: Tắt màu sắc

---

**📚 File này giúp bạn hiểu rõ cách Django khởi động development server và xử lý các tình huống khác nhau khi chạy `python manage.py runserver`**
