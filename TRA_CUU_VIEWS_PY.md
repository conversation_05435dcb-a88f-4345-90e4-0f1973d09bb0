# TRA CỨU CHI TIẾT: views.py

## 📍 VỊ TRÍ FILE
**Đường dẫn:** `django/conf/app_template/views.py-tpl` (template)
**Mục đích:** Xử lý logic business và trả về HTTP responses

---

## 🔍 PHÂN TÍCH TEMPLATE CƠ BẢN

### **Template Mặc Định (3 dòng)**
```python
from django.shortcuts import render

# Create your views here.
```

**🎯 TÁC DỤNG:**
- Import `render` function - shortcut để render templates
- Comment hướng dẫn tạo views
- File trống để developer tự định nghĩa views

**🚀 KHI NÀO DÙNG:**
- Khi chạy `python manage.py startapp` sẽ tạo file này
- Là điểm bắt đầu để định nghĩa view functions/classes

---

## 🎨 CÁC LOẠI VIEWS VÀ VÍ DỤ THỰC TẾ

### 1. **FUNCTION-BASED VIEWS (FBV) CƠ BẢN**

```python
from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse, JsonResponse, Http404
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from .models import Article, Category

# Basic view
def home(request):
    """Trang chủ đơn giản"""
    context = {
        'title': 'Trang chủ',
        'message': 'Chào mừng đến với website!'
    }
    return render(request, 'home.html', context)

# View với query database
def article_list(request):
    """Danh sách bài viết"""
    articles = Article.objects.filter(is_published=True).order_by('-pub_date')
    categories = Category.objects.all()
    
    context = {
        'articles': articles,
        'categories': categories,
        'total_count': articles.count()
    }
    return render(request, 'articles/list.html', context)

# View với parameter từ URL
def article_detail(request, article_id):
    """Chi tiết bài viết"""
    article = get_object_or_404(Article, id=article_id, is_published=True)
    
    # Tăng view count
    article.view_count += 1
    article.save(update_fields=['view_count'])
    
    # Related articles
    related_articles = Article.objects.filter(
        category=article.category,
        is_published=True
    ).exclude(id=article.id)[:5]
    
    context = {
        'article': article,
        'related_articles': related_articles
    }
    return render(request, 'articles/detail.html', context)

# View với slug parameter
def article_by_slug(request, slug):
    """Bài viết theo slug"""
    article = get_object_or_404(Article, slug=slug, is_published=True)
    return render(request, 'articles/detail.html', {'article': article})
```

**🎯 KHI NÀO DÙNG:**
- Views đơn giản, logic không phức tạp
- Khi cần control hoàn toàn flow của request
- `get_object_or_404()`: Tự động trả 404 nếu không tìm thấy
- `render()`: Shortcut để render template với context

---

### 2. **VIEWS VỚI FORM HANDLING**

```python
from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from .forms import ArticleForm, ContactForm
from .models import Article

@login_required
def article_create(request):
    """Tạo bài viết mới"""
    if request.method == 'POST':
        form = ArticleForm(request.POST, request.FILES)
        if form.is_valid():
            article = form.save(commit=False)
            article.author = request.user
            article.save()
            form.save_m2m()  # Save many-to-many relationships
            
            messages.success(request, 'Bài viết đã được tạo thành công!')
            return redirect('article_detail', article_id=article.id)
        else:
            messages.error(request, 'Có lỗi trong form. Vui lòng kiểm tra lại.')
    else:
        form = ArticleForm()
    
    return render(request, 'articles/create.html', {'form': form})

@login_required
def article_edit(request, article_id):
    """Chỉnh sửa bài viết"""
    article = get_object_or_404(Article, id=article_id, author=request.user)
    
    if request.method == 'POST':
        form = ArticleForm(request.POST, request.FILES, instance=article)
        if form.is_valid():
            form.save()
            messages.success(request, 'Bài viết đã được cập nhật!')
            return redirect('article_detail', article_id=article.id)
    else:
        form = ArticleForm(instance=article)
    
    context = {
        'form': form,
        'article': article,
        'is_edit': True
    }
    return render(request, 'articles/create.html', context)

def contact(request):
    """Form liên hệ"""
    if request.method == 'POST':
        form = ContactForm(request.POST)
        if form.is_valid():
            # Xử lý form data
            name = form.cleaned_data['name']
            email = form.cleaned_data['email']
            message = form.cleaned_data['message']
            
            # Gửi email (ví dụ)
            # send_mail(subject, message, email, ['<EMAIL>'])
            
            messages.success(request, 'Cảm ơn bạn đã liên hệ! Chúng tôi sẽ phản hồi sớm.')
            return redirect('contact')
    else:
        form = ContactForm()
    
    return render(request, 'contact.html', {'form': form})
```

**🎯 KHI NÀO DÙNG:**
- Xử lý forms (create, edit, contact)
- `commit=False`: Save model nhưng chưa vào DB để modify trước
- `save_m2m()`: Save many-to-many sau khi save main object
- `messages`: Hiển thị thông báo cho user

---

### 3. **VIEWS VỚI DECORATORS**

```python
from django.contrib.auth.decorators import login_required, permission_required
from django.views.decorators.http import require_http_methods, require_POST
from django.views.decorators.cache import cache_page
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

# Require login
@login_required
def profile(request):
    """Trang profile - cần đăng nhập"""
    return render(request, 'profile.html', {'user': request.user})

# Require specific permission
@permission_required('articles.add_article')
def admin_dashboard(request):
    """Dashboard admin - cần permission"""
    return render(request, 'admin/dashboard.html')

# Restrict HTTP methods
@require_http_methods(["GET", "POST"])
def article_form(request):
    """Chỉ cho phép GET và POST"""
    # Logic here
    pass

@require_POST
def article_delete(request, article_id):
    """Chỉ cho phép POST để xóa"""
    article = get_object_or_404(Article, id=article_id, author=request.user)
    article.delete()
    messages.success(request, 'Bài viết đã được xóa!')
    return redirect('article_list')

# Cache view
@cache_page(60 * 15)  # Cache 15 minutes
def popular_articles(request):
    """Bài viết phổ biến - có cache"""
    articles = Article.objects.filter(
        is_published=True
    ).order_by('-view_count')[:10]
    
    return render(request, 'articles/popular.html', {'articles': articles})

# CSRF exempt (cẩn thận khi dùng)
@csrf_exempt
def api_webhook(request):
    """Webhook từ external service"""
    if request.method == 'POST':
        # Process webhook data
        pass
    return HttpResponse('OK')
```

**🎯 KHI NÀO DÙNG:**
- `@login_required`: Views cần authentication
- `@permission_required`: Views cần specific permissions
- `@require_POST`: Actions như delete, update
- `@cache_page`: Views có data ít thay đổi
- `@csrf_exempt`: APIs, webhooks (cẩn thận security)

---

### 4. **JSON/API VIEWS**

```python
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
import json

def article_api(request):
    """API trả về JSON"""
    articles = Article.objects.filter(is_published=True).values(
        'id', 'title', 'slug', 'pub_date', 'view_count'
    )
    
    data = {
        'status': 'success',
        'count': len(articles),
        'articles': list(articles)
    }
    return JsonResponse(data)

def search_articles(request):
    """Search API với pagination"""
    query = request.GET.get('q', '')
    page = request.GET.get('page', 1)
    
    if query:
        articles = Article.objects.filter(
            title__icontains=query,
            is_published=True
        )
    else:
        articles = Article.objects.filter(is_published=True)
    
    # Pagination
    paginator = Paginator(articles, 10)  # 10 per page
    page_obj = paginator.get_page(page)
    
    data = {
        'status': 'success',
        'query': query,
        'total': paginator.count,
        'page': page_obj.number,
        'total_pages': paginator.num_pages,
        'has_next': page_obj.has_next(),
        'has_previous': page_obj.has_previous(),
        'articles': [
            {
                'id': article.id,
                'title': article.title,
                'slug': article.slug,
                'pub_date': article.pub_date.isoformat(),
            }
            for article in page_obj
        ]
    }
    return JsonResponse(data)

@csrf_exempt
def article_vote(request):
    """AJAX vote cho bài viết"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            article_id = data.get('article_id')
            vote_type = data.get('vote_type')  # 'up' or 'down'
            
            article = Article.objects.get(id=article_id)
            
            if vote_type == 'up':
                article.upvotes += 1
            elif vote_type == 'down':
                article.downvotes += 1
            
            article.save()
            
            return JsonResponse({
                'status': 'success',
                'upvotes': article.upvotes,
                'downvotes': article.downvotes
            })
            
        except Article.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': 'Article not found'}, status=404)
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)
    
    return JsonResponse({'status': 'error', 'message': 'Method not allowed'}, status=405)
```

**🎯 KHI NÀO DÙNG:**
- AJAX requests từ frontend
- Mobile app APIs
- Third-party integrations
- Real-time features (voting, comments)

---

### 5. **CLASS-BASED VIEWS (CBV)**

```python
from django.views.generic import (
    ListView, DetailView, CreateView, UpdateView, DeleteView,
    TemplateView, RedirectView
)
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.urls import reverse_lazy
from django.contrib import messages

# Template view
class HomeView(TemplateView):
    """Trang chủ với template view"""
    template_name = 'home.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['featured_articles'] = Article.objects.filter(
            is_featured=True, is_published=True
        )[:5]
        context['latest_articles'] = Article.objects.filter(
            is_published=True
        ).order_by('-pub_date')[:10]
        return context

# List view
class ArticleListView(ListView):
    """Danh sách bài viết"""
    model = Article
    template_name = 'articles/list.html'
    context_object_name = 'articles'
    paginate_by = 10
    
    def get_queryset(self):
        queryset = Article.objects.filter(is_published=True)
        
        # Filter by category
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category__slug=category)
        
        # Search
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(title__icontains=search)
        
        return queryset.order_by('-pub_date')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.all()
        context['search_query'] = self.request.GET.get('search', '')
        return context

# Detail view
class ArticleDetailView(DetailView):
    """Chi tiết bài viết"""
    model = Article
    template_name = 'articles/detail.html'
    context_object_name = 'article'
    
    def get_queryset(self):
        return Article.objects.filter(is_published=True)
    
    def get_object(self):
        obj = super().get_object()
        # Tăng view count
        obj.view_count += 1
        obj.save(update_fields=['view_count'])
        return obj

# Create view
class ArticleCreateView(LoginRequiredMixin, CreateView):
    """Tạo bài viết mới"""
    model = Article
    form_class = ArticleForm
    template_name = 'articles/create.html'
    success_url = reverse_lazy('article_list')
    
    def form_valid(self, form):
        form.instance.author = self.request.user
        messages.success(self.request, 'Bài viết đã được tạo thành công!')
        return super().form_valid(form)

# Update view
class ArticleUpdateView(LoginRequiredMixin, UpdateView):
    """Cập nhật bài viết"""
    model = Article
    form_class = ArticleForm
    template_name = 'articles/edit.html'
    
    def get_queryset(self):
        # Chỉ cho phép edit bài viết của mình
        return Article.objects.filter(author=self.request.user)
    
    def form_valid(self, form):
        messages.success(self.request, 'Bài viết đã được cập nhật!')
        return super().form_valid(form)

# Delete view
class ArticleDeleteView(LoginRequiredMixin, DeleteView):
    """Xóa bài viết"""
    model = Article
    template_name = 'articles/delete.html'
    success_url = reverse_lazy('article_list')
    
    def get_queryset(self):
        return Article.objects.filter(author=self.request.user)
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Bài viết đã được xóa!')
        return super().delete(request, *args, **kwargs)
```

**🎯 KHI NÀO DÙNG:**
- CRUD operations chuẩn
- Khi cần reuse code nhiều
- `LoginRequiredMixin`: Require login cho CBV
- `get_queryset()`: Custom query logic
- `get_context_data()`: Thêm data vào context

---

### 6. **CUSTOM MIXINS VÀ ADVANCED CBV**

```python
from django.contrib.auth.mixins import UserPassesTestMixin
from django.core.exceptions import PermissionDenied

class OwnerRequiredMixin(UserPassesTestMixin):
    """Mixin yêu cầu user là owner của object"""
    
    def test_func(self):
        obj = self.get_object()
        return obj.author == self.request.user

class AjaxResponseMixin:
    """Mixin để handle AJAX requests"""
    
    def dispatch(self, request, *args, **kwargs):
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return self.ajax_dispatch(request, *args, **kwargs)
        return super().dispatch(request, *args, **kwargs)
    
    def ajax_dispatch(self, request, *args, **kwargs):
        return JsonResponse({'status': 'error', 'message': 'AJAX not implemented'})

class ArticleOwnerUpdateView(OwnerRequiredMixin, UpdateView):
    """Update view chỉ cho owner"""
    model = Article
    form_class = ArticleForm
    template_name = 'articles/edit.html'
    
    def handle_no_permission(self):
        messages.error(self.request, 'Bạn không có quyền chỉnh sửa bài viết này!')
        return redirect('article_list')

class ArticleAjaxView(AjaxResponseMixin, DetailView):
    """Article view với AJAX support"""
    model = Article
    
    def ajax_dispatch(self, request, *args, **kwargs):
        article = self.get_object()
        data = {
            'id': article.id,
            'title': article.title,
            'content': article.content,
            'author': article.author.username,
        }
        return JsonResponse(data)
```

**🎯 KHI NÀO DÙNG:**
- Custom mixins để reuse logic
- `UserPassesTestMixin`: Custom permission checks
- AJAX handling trong CBV
- Complex permission logic

---

## 🎯 BEST PRACTICES VÀ PATTERNS

### ✅ **Khi nào dùng FBV vs CBV:**

**Function-Based Views (FBV):**
- Logic đơn giản, specific
- Cần control hoàn toàn flow
- Views có logic phức tạp, không chuẩn

**Class-Based Views (CBV):**
- CRUD operations chuẩn
- Cần reuse code
- Khi có nhiều views tương tự

### 🔧 **Common Patterns:**

1. **Redirect after POST:**
   ```python
   # Luôn redirect sau POST để tránh duplicate submission
   if form.is_valid():
       form.save()
       return redirect('success_url')
   ```

2. **Messages framework:**
   ```python
   messages.success(request, 'Success message')
   messages.error(request, 'Error message')
   messages.warning(request, 'Warning message')
   ```

3. **Pagination:**
   ```python
   from django.core.paginator import Paginator
   
   paginator = Paginator(object_list, 25)
   page_obj = paginator.get_page(page_number)
   ```

---

**📚 File này cung cấp patterns và examples để tạo Django views từ cơ bản đến nâng cao với FBV, CBV, forms, APIs, và best practices**
