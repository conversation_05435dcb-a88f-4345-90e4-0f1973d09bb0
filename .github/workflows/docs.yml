name: Docs

on:
  pull_request:
    paths:
      - 'docs/**'
      - '.github/workflows/docs.yml'
  push:
    branches:
      - main
    paths:
      - 'docs/**'
      - '.github/workflows/docs.yml'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  docs:
    runs-on: ubuntu-24.04
    name: docs
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.13'
          cache: 'pip'
          cache-dependency-path: 'docs/requirements.txt'
      - run: python -m pip install -r docs/requirements.txt
      - name: Build docs
        run: |
          cd docs
          sphinx-build -b spelling -n -q -W --keep-going -d _build/doctrees -D language=en_US -j auto . _build/spelling

  blacken-docs:
    runs-on: ubuntu-latest
    name: blacken-docs
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.13'
      - run: python -m pip install blacken-docs
      - name: Build docs
        run: |
          cd docs
          make black
          RESULT=`cat _build/black/output.txt`
          if [ "$RESULT" -gt 0 ]; then
              echo "💥 📢 Code blocks in documentation must be reformatted with blacken-docs 📢 💥"
          fi;
          exit $RESULT
