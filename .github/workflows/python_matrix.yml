name: Python Matrix from config file

on:
  pull_request:
    types: [labeled, synchronize, opened, reopened]
    paths-ignore:
      - 'docs/**'
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
   contents: read

jobs:
  define-matrix:
    if: contains(github.event.pull_request.labels.*.name, 'python-matrix')
    runs-on: ubuntu-latest
    outputs:
      python_versions_output: ${{ steps.set-matrix.outputs.python_versions }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - id: set-matrix
        run: |
          python_versions=$(sed -n "s/^.*Programming Language :: Python :: \([[:digit:]]\+\.[[:digit:]]\+\).*$/'\1', /p" pyproject.toml | tr -d '\n' | sed 's/, $//g')
          echo "Supported Python versions: $python_versions"
          echo "python_versions=[$python_versions]" >> "$GITHUB_OUTPUT"
  python:
    runs-on: ubuntu-latest
    needs: define-matrix
    strategy:
      matrix:
        python-version: ${{ fromJson(needs.define-matrix.outputs.python_versions_output) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'pip'
          cache-dependency-path: 'tests/requirements/py3.txt'
      - name: Install libmemcached-dev for pylibmc
        run: sudo apt-get install libmemcached-dev
      - name: Install and upgrade packaging tools
        run: python -m pip install --upgrade pip setuptools wheel
      - run: python -m pip install -r tests/requirements/py3.txt -e .
      - name: Run tests
        run: python -Wall tests/runtests.py -v2
