name: Schedule tests

on:
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  windows:
    runs-on: windows-latest
    strategy:
      matrix:
        python-version:
          - '3.12'
          - '3.13'
          - '3.14-dev'
    name: Windows, SQLite, Python ${{ matrix.python-version }}
    continue-on-error: true
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'pip'
          cache-dependency-path: 'tests/requirements/py3.txt'
      - name: Install and upgrade packaging tools
        run: python -m pip install --upgrade pip setuptools wheel
      - run: python -m pip install -r tests/requirements/py3.txt -e .
      - name: Run tests
        run: python -Wall tests/runtests.py -v2

  pyc-only:
    runs-on: ubuntu-latest
    name: Byte-compiled Django with no source files (only .pyc files)
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.13'
          cache: 'pip'
      - name: Install libmemcached-dev for pylibmc
        run: sudo apt-get install libmemcached-dev
      - name: Install and upgrade packaging tools
        run: python -m pip install --upgrade pip setuptools wheel
      - run: python -m pip install .
      - name: Prepare site-packages
        run: |
          DJANGO_PACKAGE_ROOT=$(python -c 'import site; print(site.getsitepackages()[0])')/django
          echo $DJANGO_PACKAGE_ROOT
          python -m compileall -b $DJANGO_PACKAGE_ROOT
          find $DJANGO_PACKAGE_ROOT -name '*.py' -print -delete
      - run: python -m pip install -r tests/requirements/py3.txt
      - name: Run tests
        run: python -Wall tests/runtests.py --verbosity=2

  javascript-tests:
    runs-on: ubuntu-latest
    name: JavaScript tests
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: '**/package.json'
      - run: npm install
      - run: npm test

  selenium-sqlite:
    runs-on: ubuntu-latest
    name: Selenium tests, SQLite
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.13'
          cache: 'pip'
          cache-dependency-path: 'tests/requirements/py3.txt'
      - name: Install libmemcached-dev for pylibmc
        run: sudo apt-get install libmemcached-dev
      - name: Install and upgrade packaging tools
        run: python -m pip install --upgrade pip setuptools wheel
      - run: python -m pip install -r tests/requirements/py3.txt -e .
      - name: Run Selenium tests
        working-directory: ./tests/
        run: |
          python -Wall runtests.py --verbosity 2 --noinput --selenium=chrome --headless --settings=test_sqlite --parallel 2

  selenium-postgresql:
    runs-on: ubuntu-latest
    name: Selenium tests, PostgreSQL
    services:
      postgres:
        image: postgres:14-alpine
        env:
          POSTGRES_DB: django
          POSTGRES_USER: user
          POSTGRES_PASSWORD: postgres
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.13'
          cache: 'pip'
          cache-dependency-path: 'tests/requirements/py3.txt'
      - name: Install libmemcached-dev for pylibmc
        run: sudo apt-get install libmemcached-dev
      - name: Install and upgrade packaging tools
        run: python -m pip install --upgrade pip setuptools wheel
      - run: python -m pip install -r tests/requirements/py3.txt -r tests/requirements/postgres.txt -e .
      - name: Create PostgreSQL settings file
        run: mv ./.github/workflows/data/test_postgres.py.tpl ./tests/test_postgres.py
      - name: Run Selenium tests
        working-directory: ./tests/
        run: |
          python -Wall runtests.py --verbosity 2 --noinput --selenium=chrome --headless --settings=test_postgres --parallel 2

  postgresql:
    strategy:
      fail-fast: false
      matrix:
        version: [16, 17]
        server_side_bindings: [0, 1]
    runs-on: ubuntu-latest
    name: PostgreSQL Versions
    env:
      SERVER_SIDE_BINDING: ${{ matrix.server_side_bindings }}
    services:
      postgres:
        image: postgres:${{ matrix.version }}-alpine
        env:
          POSTGRES_DB: django
          POSTGRES_USER: user
          POSTGRES_PASSWORD: postgres
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.13'
          cache: 'pip'
          cache-dependency-path: 'tests/requirements/py3.txt'
      - name: Install libmemcached-dev for pylibmc
        run: sudo apt-get install libmemcached-dev
      - name: Install and upgrade packaging tools
        run: python -m pip install --upgrade pip setuptools wheel
      - run: python -m pip install -r tests/requirements/py3.txt -r tests/requirements/postgres.txt -e .
      - name: Create PostgreSQL settings file
        run: mv ./.github/workflows/data/test_postgres.py.tpl ./tests/test_postgres.py
      - name: Run tests
        working-directory: ./tests/
        run: python -Wall runtests.py --settings=test_postgres --verbosity=2
