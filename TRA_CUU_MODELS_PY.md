# TRA CỨU CHI TIẾT: models.py

## 📍 VỊ TRÍ FILE
**Đường dẫn:** `django/conf/app_template/models.py-tpl` (template)
**Mục đích:** Đ<PERSON><PERSON> nghĩa cấu trúc dữ liệu và business logic

---

## 🔍 PHÂN TÍCH TEMPLATE CƠ BẢN

### **Template Mặc Định (3 dòng)**
```python
from django.db import models

# Create your models here.
```

**🎯 TÁC DỤNG:**
- Import `models` module từ Django
- Comment hướng dẫn tạo models
- File trống để developer tự định nghĩa

**🚀 KHI NÀO DÙNG:**
- <PERSON>hi chạy `python manage.py startapp` sẽ tạo file này
- Là điểm bắt đầu để định nghĩa models

---

## 🏗️ CÁC LOẠI MODELS VÀ VÍ DỤ THỰC TẾ

### 1. **MODEL CƠ BẢN - SINGLE TABLE**

```python
from django.db import models
from django.contrib.auth.models import User

class Article(models.Model):
    """Model đơn giản cho bài viết"""
    
    # Text fields
    headline = models.CharField(max_length=100, verbose_name="Tiêu đề")
    content = models.TextField(help_text="Nội dung bài viết")
    slug = models.SlugField(max_length=100, unique=True)
    
    # Date/Time fields
    pub_date = models.DateField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Boolean field
    is_published = models.BooleanField(default=False)
    
    # Number fields
    view_count = models.PositiveIntegerField(default=0)
    rating = models.FloatField(null=True, blank=True)
    
    def __str__(self):
        return self.headline
    
    def was_published_today(self):
        """Custom method - kiểm tra có publish hôm nay không"""
        return self.pub_date == timezone.now().date()
    
    class Meta:
        ordering = ['-pub_date']
        verbose_name = "Bài viết"
        verbose_name_plural = "Bài viết"
```

**🎯 KHI NÀO DÙNG:**
- Model đơn giản, không có relationship phức tạp
- Blog posts, news articles, simple content
- Khi cần các field cơ bản: text, date, boolean, number

---

### 2. **MODEL VỚI FOREIGN KEY RELATIONSHIPS**

```python
class Author(models.Model):
    """Tác giả"""
    first_name = models.CharField(max_length=30)
    last_name = models.CharField(max_length=30)
    email = models.EmailField(unique=True)
    bio = models.TextField(blank=True)
    birth_date = models.DateField(null=True, blank=True)
    
    def __str__(self):
        return f"{self.first_name} {self.last_name}"
    
    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"

class Publisher(models.Model):
    """Nhà xuất bản"""
    name = models.CharField(max_length=100)
    address = models.TextField()
    website = models.URLField(blank=True)
    
    def __str__(self):
        return self.name

class Book(models.Model):
    """Sách với relationships"""
    title = models.CharField(max_length=200)
    isbn = models.CharField(max_length=13, unique=True)
    pages = models.PositiveIntegerField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Foreign Key relationships
    author = models.ForeignKey(
        Author, 
        on_delete=models.CASCADE,
        related_name='books',
        help_text="Tác giả chính"
    )
    publisher = models.ForeignKey(
        Publisher,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='published_books'
    )
    
    # Date fields
    publication_date = models.DateField()
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return self.title
    
    def get_absolute_url(self):
        return f"/books/{self.id}/"
    
    class Meta:
        ordering = ['-publication_date']
        unique_together = ['title', 'author']  # Không cho phép trùng
```

**🎯 KHI NÀO DÙNG:**
- Khi có relationship 1-to-many (một tác giả nhiều sách)
- `on_delete=models.CASCADE`: Xóa author → xóa tất cả books
- `on_delete=models.SET_NULL`: Xóa publisher → set null cho books
- `related_name`: Truy cập ngược từ Author → books

---

### 3. **MODEL VỚI MANY-TO-MANY RELATIONSHIPS**

```python
class Category(models.Model):
    """Danh mục"""
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True)
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name_plural = "Categories"

class Tag(models.Model):
    """Tag"""
    name = models.CharField(max_length=30, unique=True)
    color = models.CharField(max_length=7, default="#000000")  # Hex color
    
    def __str__(self):
        return self.name

class Post(models.Model):
    """Bài viết với many-to-many"""
    title = models.CharField(max_length=200)
    content = models.TextField()
    author = models.ForeignKey(User, on_delete=models.CASCADE)
    
    # Many-to-Many relationships
    categories = models.ManyToManyField(
        Category,
        related_name='posts',
        blank=True,
        help_text="Chọn danh mục cho bài viết"
    )
    tags = models.ManyToManyField(
        Tag,
        through='PostTag',  # Custom intermediate table
        related_name='posts'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.title

class PostTag(models.Model):
    """Intermediate model cho Post-Tag relationship"""
    post = models.ForeignKey(Post, on_delete=models.CASCADE)
    tag = models.ForeignKey(Tag, on_delete=models.CASCADE)
    added_by = models.ForeignKey(User, on_delete=models.CASCADE)
    added_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['post', 'tag']
```

**🎯 KHI NÀO DÙNG:**
- Many-to-many relationships (một post nhiều categories, một category nhiều posts)
- `through`: Khi cần thêm fields vào relationship table
- `blank=True`: Cho phép không chọn categories

---

### 4. **MODEL VỚI CHOICES VÀ ENUMS**

```python
class Order(models.Model):
    """Đơn hàng với choices"""
    
    # Status choices
    PENDING = 'pending'
    PROCESSING = 'processing'
    SHIPPED = 'shipped'
    DELIVERED = 'delivered'
    CANCELLED = 'cancelled'
    
    STATUS_CHOICES = [
        (PENDING, 'Đang chờ'),
        (PROCESSING, 'Đang xử lý'),
        (SHIPPED, 'Đã gửi'),
        (DELIVERED, 'Đã giao'),
        (CANCELLED, 'Đã hủy'),
    ]
    
    # Priority choices với nested groups
    PRIORITY_CHOICES = [
        ('Urgent', [
            ('high', 'Cao'),
            ('critical', 'Khẩn cấp'),
        ]),
        ('Normal', [
            ('medium', 'Trung bình'),
            ('low', 'Thấp'),
        ]),
    ]
    
    order_number = models.CharField(max_length=20, unique=True)
    customer = models.ForeignKey(User, on_delete=models.CASCADE)
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=PENDING,
        help_text="Trạng thái đơn hàng"
    )
    priority = models.CharField(
        max_length=20,
        choices=PRIORITY_CHOICES,
        default='medium'
    )
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Order {self.order_number}"
    
    def get_status_display_color(self):
        """Custom method trả về màu cho status"""
        colors = {
            self.PENDING: 'orange',
            self.PROCESSING: 'blue',
            self.SHIPPED: 'purple',
            self.DELIVERED: 'green',
            self.CANCELLED: 'red',
        }
        return colors.get(self.status, 'gray')
    
    class Meta:
        ordering = ['-created_at']
```

**🎯 KHI NÀO DÙNG:**
- Khi field có giá trị cố định (status, priority, gender, etc.)
- `get_FOO_display()`: Django tự tạo method để lấy display value
- Nested choices cho grouping options

---

### 5. **MODEL VỚI FILE/IMAGE FIELDS**

```python
import os
from django.core.validators import FileExtensionValidator

def user_avatar_path(instance, filename):
    """Custom upload path cho avatar"""
    return f'avatars/user_{instance.user.id}/{filename}'

def document_upload_path(instance, filename):
    """Custom upload path cho documents"""
    ext = filename.split('.')[-1]
    filename = f'{instance.title}.{ext}'
    return f'documents/{instance.category.name}/{filename}'

class UserProfile(models.Model):
    """Profile với image field"""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    bio = models.TextField(max_length=500, blank=True)
    location = models.CharField(max_length=30, blank=True)
    birth_date = models.DateField(null=True, blank=True)
    
    # Image field với custom upload path
    avatar = models.ImageField(
        upload_to=user_avatar_path,
        null=True,
        blank=True,
        help_text="Ảnh đại diện (tối đa 2MB)"
    )
    
    def __str__(self):
        return f"{self.user.username}'s profile"

class Document(models.Model):
    """Document với file field và validation"""
    
    CATEGORY_CHOICES = [
        ('contract', 'Hợp đồng'),
        ('invoice', 'Hóa đơn'),
        ('report', 'Báo cáo'),
        ('other', 'Khác'),
    ]
    
    title = models.CharField(max_length=200)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    description = models.TextField(blank=True)
    
    # File field với validators
    file = models.FileField(
        upload_to=document_upload_path,
        validators=[
            FileExtensionValidator(
                allowed_extensions=['pdf', 'doc', 'docx', 'txt']
            )
        ],
        help_text="Chỉ chấp nhận: PDF, DOC, DOCX, TXT"
    )
    
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    file_size = models.PositiveIntegerField(null=True, blank=True)
    
    def save(self, *args, **kwargs):
        """Override save để tính file size"""
        if self.file:
            self.file_size = self.file.size
        super().save(*args, **kwargs)
    
    def get_file_size_display(self):
        """Hiển thị file size dễ đọc"""
        if self.file_size:
            if self.file_size < 1024:
                return f"{self.file_size} bytes"
            elif self.file_size < 1024 * 1024:
                return f"{self.file_size / 1024:.1f} KB"
            else:
                return f"{self.file_size / (1024 * 1024):.1f} MB"
        return "Unknown"
    
    def __str__(self):
        return self.title
    
    class Meta:
        ordering = ['-uploaded_at']
```

**🎯 KHI NÀO DÙNG:**
- User avatars, document uploads, media files
- Custom upload paths để organize files
- File validation để security
- Override save() để custom logic

---

### 6. **ABSTRACT BASE MODEL**

```python
class TimestampedModel(models.Model):
    """Abstract base model với timestamp fields"""
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        abstract = True  # Không tạo table riêng

class SoftDeleteModel(models.Model):
    """Abstract model với soft delete"""
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        abstract = True
    
    def soft_delete(self):
        """Soft delete method"""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()

class Product(TimestampedModel, SoftDeleteModel):
    """Product kế thừa từ abstract models"""
    name = models.CharField(max_length=200)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    sku = models.CharField(max_length=50, unique=True)
    stock_quantity = models.PositiveIntegerField(default=0)
    
    def __str__(self):
        return self.name
    
    def is_in_stock(self):
        return self.stock_quantity > 0 and not self.is_deleted
```

**🎯 KHI NÀO DÙNG:**
- Khi nhiều models cần chung fields (timestamps, soft delete)
- `abstract = True`: Không tạo database table
- Multiple inheritance từ nhiều abstract models
- DRY principle - không lặp lại code

---

## 🔧 META OPTIONS QUAN TRỌNG

```python
class MyModel(models.Model):
    name = models.CharField(max_length=100)
    
    class Meta:
        # Database table name
        db_table = 'custom_table_name'
        
        # Ordering
        ordering = ['-created_at', 'name']  # Desc created_at, asc name
        
        # Verbose names
        verbose_name = 'My Model'
        verbose_name_plural = 'My Models'
        
        # Unique constraints
        unique_together = [['field1', 'field2']]
        
        # Indexes
        indexes = [
            models.Index(fields=['name', 'created_at']),
            models.Index(fields=['name'], name='name_idx'),
        ]
        
        # Permissions
        permissions = [
            ('can_publish', 'Can publish items'),
            ('can_edit_all', 'Can edit all items'),
        ]
        
        # Abstract model
        abstract = True
        
        # Proxy model
        proxy = True
        
        # Default permissions
        default_permissions = ('add', 'change', 'delete', 'view')
        
        # App label
        app_label = 'myapp'
```

**🎯 KHI NÀO DÙNG:**
- `db_table`: Custom table name
- `ordering`: Default sort order
- `unique_together`: Composite unique constraints
- `indexes`: Performance optimization
- `permissions`: Custom permissions cho Django admin

---

**📚 File này cung cấp template và patterns để tạo Django models từ cơ bản đến nâng cao với các relationships, validations, và best practices**
