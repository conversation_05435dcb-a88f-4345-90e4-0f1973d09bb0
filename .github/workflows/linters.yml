name: Linters

on:
  pull_request:
    paths-ignore:
      - 'docs/**'
  push:
    branches:
      - main
    paths-ignore:
      - 'docs/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  flake8:
    name: flake8
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.13'
      - run: python -m pip install flake8
      - name: flake8
        # Pinned to v3.0.0.
        uses: liskin/gh-problem-matcher-wrap@e7b7beaaafa52524748b31a381160759d68d61fb
        with:
          linters: flake8
          run: flake8

  isort:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.13'
      - run: python -m pip install "isort<6"
      - name: isort
        # Pinned to v3.0.0.
        uses: liskin/gh-problem-matcher-wrap@e7b7beaaafa52524748b31a381160759d68d61fb
        with:
          linters: isort
          run: isort --check --diff django tests scripts

  black:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: black
        uses: psf/black@stable
