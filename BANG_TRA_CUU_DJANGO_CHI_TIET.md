# BẢNG TRA CỨU DJANGO CHI TIẾT - TỪ ĐIỂN CODE HOÀN CHỈNH

## 📋 MỤC LỤC
1. [<PERSON><PERSON><PERSON> Django Management Commands](#các-lệnh-django-management-commands)
2. [File Cấu Hình Quan Trọng](#file-cấu-hình-quan-trọng)
3. [File Template <PERSON><PERSON> Bản](#file-template-cơ-bản)
4. [Th<PERSON>c và Cấu Trúc Project](#thư-mục-và-cấu-trúc-project)
5. [File Scripts và Utilities](#file-scripts-và-utilities)

---

## 🔧 CÁC LỆNH DJANGO MANAGEMENT COMMANDS

### 📍 VỊ TRÍ: `django/core/management/commands/`

### 1. **manage.py** - File Quản Lý Chính
**Vị trí:** `django/conf/project_template/manage.py-tpl`
**Mục đích:** Đi<PERSON>m khởi đầu cho tất cả các lệnh Django
**<PERSON><PERSON> pháp:**
```bash
python manage.py <command> [options]
```
**Chức năng:** 
- <PERSON><PERSON><PERSON><PERSON> lập biến môi trường DJANGO_SETTINGS_MODULE
- Gọi execute_from_command_line() để thực thi lệnh
- Xử lý import Django và báo lỗi nếu không tìm thấy

### 2. **startproject** - Tạo Dự Án Mới
**Vị trí:** `django/core/management/commands/startproject.py`
**Cú pháp:**
```bash
django-admin startproject <project_name> [directory]
python manage.py startproject <project_name> [directory]
```
**Chức năng:**
- Tạo cấu trúc thư mục dự án Django mới
- Tạo file settings.py, urls.py, wsgi.py, asgi.py
- Tạo file manage.py cho dự án
- Hỗ trợ template tùy chỉnh với --template

### 3. **startapp** - Tạo Ứng Dụng Mới
**Vị trí:** `django/core/management/commands/startapp.py`
**Cú pháp:**
```bash
python manage.py startapp <app_name> [directory]
```
**Chức năng:**
- Tạo cấu trúc thư mục ứng dụng Django
- Tạo file models.py, views.py, admin.py, apps.py
- Tạo thư mục migrations/ và tests.py
- Hỗ trợ template tùy chỉnh

### 4. **runserver** - Chạy Server Phát Triển
**Vị trí:** `django/core/management/commands/runserver.py`
**Cú pháp:**
```bash
python manage.py runserver [addrport]
python manage.py runserver 8080
python manage.py runserver 0.0.0.0:8000
```
**Tham số quan trọng:**
- `--noreload`: Tắt auto-reload
- `--nothreading`: Tắt threading
- `--ipv6`: Sử dụng IPv6
**Chức năng:**
- Khởi động server phát triển Django
- Tự động reload khi file thay đổi
- Phục vụ static files (nếu có staticfiles app)

### 5. **makemigrations** - Tạo Migration Files
**Vị trí:** `django/core/management/commands/makemigrations.py`
**Cú pháp:**
```bash
python manage.py makemigrations [app_label]
python manage.py makemigrations --dry-run
python manage.py makemigrations --empty <app_name>
```
**Tham số quan trọng:**
- `--dry-run`: Xem trước không tạo file
- `--empty`: Tạo migration rỗng
- `--name`: Đặt tên cho migration
- `--merge`: Merge conflicting migrations
**Chức năng:**
- Phát hiện thay đổi trong models
- Tạo file migration tương ứng
- Xử lý dependencies giữa các migration

### 6. **migrate** - Áp Dụng Migrations
**Vị trí:** `django/core/management/commands/migrate.py`
**Cú pháp:**
```bash
python manage.py migrate
python manage.py migrate <app_name>
python manage.py migrate <app_name> <migration_name>
```
**Tham số quan trọng:**
- `--fake`: Đánh dấu migration đã chạy mà không thực thi
- `--fake-initial`: Fake migration đầu tiên
- `--plan`: Xem kế hoạch migration
- `--database`: Chọn database
**Chức năng:**
- Áp dụng migration vào database
- Cập nhật bảng django_migrations
- Xử lý dependencies và thứ tự migration

### 7. **showmigrations** - Hiển Thị Trạng Thái Migration
**Vị trí:** `django/core/management/commands/showmigrations.py`
**Cú pháp:**
```bash
python manage.py showmigrations
python manage.py showmigrations --list
python manage.py showmigrations --plan
```
**Chức năng:**
- Hiển thị danh sách tất cả migrations
- Cho biết migration nào đã được áp dụng ([X])
- Hiển thị kế hoạch migration với --plan

### 8. **shell** - Mở Python Shell
**Vị trí:** `django/core/management/commands/shell.py`
**Cú pháp:**
```bash
python manage.py shell
python manage.py shell --interface=ipython
python manage.py shell --command="print('Hello')"
```
**Tham số quan trọng:**
- `--interface`: Chọn shell (ipython, bpython, python)
- `--command`: Chạy lệnh trực tiếp
**Chức năng:**
- Mở Python shell với Django environment
- Tự động import Django settings
- Hỗ trợ nhiều loại shell khác nhau

### 9. **test** - Chạy Unit Tests
**Vị trí:** `django/core/management/commands/test.py`
**Cú pháp:**
```bash
python manage.py test
python manage.py test <app_name>
python manage.py test <app_name.TestClass>
python manage.py test <app_name.TestClass.test_method>
```
**Tham số quan trọng:**
- `--keepdb`: Giữ test database
- `--parallel`: Chạy test song song
- `--verbosity`: Mức độ chi tiết output
- `--failfast`: Dừng khi gặp lỗi đầu tiên
**Chức năng:**
- Chạy unit tests cho ứng dụng
- Tạo test database tạm thời
- Báo cáo kết quả test chi tiết

### 10. **collectstatic** - Thu Thập Static Files
**Vị trí:** `django/contrib/staticfiles/management/commands/collectstatic.py`
**Cú pháp:**
```bash
python manage.py collectstatic
python manage.py collectstatic --noinput
python manage.py collectstatic --clear
```
**Tham số quan trọng:**
- `--noinput`: Không hỏi xác nhận
- `--clear`: Xóa file cũ trước khi copy
- `--dry-run`: Xem trước không thực hiện
- `--link`: Tạo symlink thay vì copy
**Chức năng:**
- Thu thập static files từ tất cả apps
- Copy vào thư mục STATIC_ROOT
- Xử lý static files cho production

### 11. **check** - Kiểm Tra Hệ Thống
**Vị trí:** `django/core/management/commands/check.py`
**Cú pháp:**
```bash
python manage.py check
python manage.py check --deploy
python manage.py check --tag=models
```
**Tham số quan trọng:**
- `--deploy`: Kiểm tra cấu hình production
- `--tag`: Kiểm tra tag cụ thể
- `--list-tags`: Liệt kê tất cả tags
**Chức năng:**
- Kiểm tra lỗi cấu hình Django
- Validate models, URLs, settings
- Đưa ra cảnh báo và lỗi

### 12. **flush** - Xóa Dữ Liệu Database
**Vị trí:** `django/core/management/commands/flush.py`
**Cú pháp:**
```bash
python manage.py flush
python manage.py flush --noinput
```
**Chức năng:**
- Xóa tất cả dữ liệu trong database
- Giữ nguyên cấu trúc bảng
- Reset auto-increment counters

### 13. **loaddata** - Nạp Dữ Liệu Fixture
**Vị trí:** `django/core/management/commands/loaddata.py`
**Cú pháp:**
```bash
python manage.py loaddata <fixture_name>
python manage.py loaddata data.json
python manage.py loaddata data.xml
```
**Chức năng:**
- Nạp dữ liệu từ fixture files
- Hỗ trợ JSON, XML, YAML format
- Tìm kiếm trong thư mục fixtures/

### 14. **dumpdata** - Xuất Dữ Liệu
**Vị trí:** `django/core/management/commands/dumpdata.py`
**Cú pháp:**
```bash
python manage.py dumpdata
python manage.py dumpdata <app_name>
python manage.py dumpdata <app_name.Model>
python manage.py dumpdata --format=json
```
**Tham số quan trọng:**
- `--format`: Định dạng output (json, xml, yaml)
- `--indent`: Thụt lề cho JSON
- `--natural-foreign`: Sử dụng natural keys
- `--exclude`: Loại trừ models
**Chức năng:**
- Xuất dữ liệu database thành fixture
- Hỗ trợ nhiều định dạng
- Có thể xuất toàn bộ hoặc một phần

### 15. **inspectdb** - Tạo Models Từ Database
**Vị trí:** `django/core/management/commands/inspectdb.py`
**Cú pháp:**
```bash
python manage.py inspectdb
python manage.py inspectdb table_name
python manage.py inspectdb > models.py
```
**Chức năng:**
- Tạo Django models từ database hiện có
- Phân tích cấu trúc bảng và tạo fields
- Hỗ trợ reverse engineering database

---

## ⚙️ FILE CẤU HÌNH QUAN TRỌNG

### 1. **settings.py** - File Cấu Hình Chính
**Vị trí:** `django/conf/global_settings.py` (mặc định)
**Mục đích:** Cấu hình toàn bộ dự án Django
**Các thiết lập quan trọng:**
```python
# Cơ bản
DEBUG = True/False
SECRET_KEY = 'your-secret-key'
ALLOWED_HOSTS = ['localhost', '127.0.0.1']

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# Ứng dụng
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    # ... các app khác
]

# Middleware
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    # ... middleware khác
]

# Static files
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [BASE_DIR / 'static']

# Templates
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]
```

### 2. **urls.py** - Cấu Hình URL Routing
**Vị trí:** `django/conf/project_template/project_name/urls.py-tpl`
**Mục đích:** Định tuyến URL đến views
**Cấu trúc cơ bản:**
```python
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('myapp.urls')),
    path('api/', include('api.urls')),
]
```
**Các pattern quan trọng:**
- `path()`: URL pattern cơ bản
- `re_path()`: URL pattern với regex
- `include()`: Include URLs từ app khác

---

## 📁 FILE TEMPLATE CỞ BẢN

### 1. **models.py** - Định Nghĩa Models
**Vị trí:** `django/conf/app_template/models.py-tpl`
**Mục đích:** Định nghĩa cấu trúc dữ liệu
**Template cơ bản:**
```python
from django.db import models

# Create your models here.
class MyModel(models.Model):
    name = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return self.name
```

### 2. **views.py** - Xử Lý Logic Views
**Vị trí:** `django/conf/app_template/views.py-tpl`
**Mục đích:** Xử lý request và trả về response
**Template cơ bản:**
```python
from django.shortcuts import render

# Create your views here.
def my_view(request):
    return render(request, 'template.html', context)
```

### 3. **admin.py** - Cấu Hình Django Admin
**Vị trí:** `django/conf/app_template/admin.py-tpl`
**Mục đích:** Đăng ký models với Django Admin
**Template cơ bản:**
```python
from django.contrib import admin

# Register your models here.
admin.site.register(MyModel)
```

---

## 📂 THƯ MỤC VÀ CẤU TRÚC PROJECT

### Cấu Trúc Dự Án Django Chuẩn:
```
myproject/
├── manage.py
├── myproject/
│   ├── __init__.py
│   ├── settings.py
│   ├── urls.py
│   ├── wsgi.py
│   └── asgi.py
├── myapp/
│   ├── __init__.py
│   ├── admin.py
│   ├── apps.py
│   ├── models.py
│   ├── views.py
│   ├── urls.py
│   ├── tests.py
│   ├── migrations/
│   │   └── __init__.py
│   ├── templates/
│   └── static/
├── static/
├── media/
├── templates/
└── requirements.txt
```

---

## 🛠️ FILE SCRIPTS VÀ UTILITIES

### 1. **manage_translations.py**
**Vị trí:** `scripts/manage_translations.py`
**Mục đích:** Quản lý bản dịch Django
**Các lệnh:**
- `update_catalogs`: Cập nhật catalog dịch
- `lang_stats`: Thống kê ngôn ngữ
- `fetch`: Tải dịch từ Transifex

### 2. **pyproject.toml**
**Vị trí:** `pyproject.toml`
**Mục đích:** Cấu hình build và dependencies Python
**Chứa:** Build system, dependencies, tool configs

### 3. **tox.ini**
**Vị trí:** `tox.ini`
**Mục đích:** Cấu hình testing với nhiều Python versions
**Chức năng:** Automated testing, linting, coverage

---

## 🔍 CÁC LỆNH DJANGO MANAGEMENT COMMANDS BỔ SUNG

### 16. **createsuperuser** - Tạo Tài Khoản Admin
**Vị trí:** `django/contrib/auth/management/commands/createsuperuser.py`
**Cú pháp:**
```bash
python manage.py createsuperuser
python manage.py createsuperuser --username=admin --email=<EMAIL>
```
**Tham số quan trọng:**
- `--username`: Tên đăng nhập
- `--email`: Email admin
- `--noinput`: Không hỏi input (dùng với script)
**Chức năng:**
- Tạo tài khoản superuser cho Django Admin
- Có thể tạo tự động hoặc interactive
- Validate username và email

### 17. **changepassword** - Đổi Mật Khẩu User
**Cú pháp:**
```bash
python manage.py changepassword <username>
```
**Chức năng:**
- Đổi mật khẩu cho user cụ thể
- Validate mật khẩu mới
- Cập nhật trong database

### 18. **dbshell** - Mở Database Shell
**Vị trí:** `django/core/management/commands/dbshell.py`
**Cú pháp:**
```bash
python manage.py dbshell
python manage.py dbshell --database=other_db
```
**Chức năng:**
- Mở command-line interface của database
- Tự động kết nối với database được cấu hình
- Hỗ trợ PostgreSQL, MySQL, SQLite, Oracle

### 19. **sqlmigrate** - Xem SQL Của Migration
**Vị trí:** `django/core/management/commands/sqlmigrate.py`
**Cú pháp:**
```bash
python manage.py sqlmigrate <app_name> <migration_name>
python manage.py sqlmigrate myapp 0001
```
**Chức năng:**
- Hiển thị SQL sẽ được thực thi cho migration
- Không thực thi SQL, chỉ xem trước
- Hữu ích để debug migration

### 20. **sqlflush** - Xem SQL Để Flush Database
**Vị trí:** `django/core/management/commands/sqlflush.py`
**Cú pháp:**
```bash
python manage.py sqlflush
```
**Chức năng:**
- Hiển thị SQL commands để xóa tất cả dữ liệu
- Không thực thi, chỉ xem trước
- Tương tự flush nhưng chỉ show SQL

### 21. **sqlsequencereset** - Reset Auto-increment
**Vị trí:** `django/core/management/commands/sqlsequencereset.py`
**Cú pháp:**
```bash
python manage.py sqlsequencereset <app_name>
```
**Chức năng:**
- Tạo SQL để reset auto-increment sequences
- Hữu ích sau khi import dữ liệu
- Chỉ hiển thị SQL, không thực thi

### 22. **diffsettings** - So Sánh Settings
**Vị trí:** `django/core/management/commands/diffsettings.py`
**Cú pháp:**
```bash
python manage.py diffsettings
python manage.py diffsettings --all
```
**Chức năng:**
- So sánh settings hiện tại với default settings
- Hiển thị những settings đã được thay đổi
- `--all` hiển thị tất cả settings

### 23. **sendtestemail** - Gửi Email Test
**Vị trí:** `django/core/management/commands/sendtestemail.py`
**Cú pháp:**
```bash
python manage.<NAME_EMAIL>
```
**Chức năng:**
- Gửi email test để kiểm tra cấu hình email
- Validate email settings
- Debug email configuration

### 24. **testserver** - Chạy Test Server Với Fixtures
**Vị trí:** `django/core/management/commands/testserver.py`
**Cú pháp:**
```bash
python manage.py testserver fixture1.json fixture2.json
```
**Chức năng:**
- Chạy server với dữ liệu test từ fixtures
- Tạo test database tạm thời
- Hữu ích để demo với dữ liệu mẫu

### 25. **createcachetable** - Tạo Bảng Cache
**Vị trí:** `django/core/management/commands/createcachetable.py`
**Cú pháp:**
```bash
python manage.py createcachetable
python manage.py createcachetable cache_table_name
```
**Chức năng:**
- Tạo bảng database cho cache backend
- Cần thiết khi dùng database cache
- Tạo bảng với cấu trúc phù hợp

### 26. **compilemessages** - Biên Dịch Translation Files
**Vị trí:** `django/core/management/commands/compilemessages.py`
**Cú pháp:**
```bash
python manage.py compilemessages
python manage.py compilemessages --locale=vi
```
**Tham số quan trọng:**
- `--locale`: Biên dịch locale cụ thể
- `--exclude`: Loại trừ locale
- `--use-fuzzy`: Bao gồm fuzzy translations
**Chức năng:**
- Biên dịch .po files thành .mo files
- Cần thiết để hiển thị translations
- Xử lý internationalization

### 27. **makemessages** - Tạo Translation Files
**Vị trí:** `django/core/management/commands/makemessages.py`
**Cú pháp:**
```bash
python manage.py makemessages --locale=vi
python manage.py makemessages --all
```
**Tham số quan trọng:**
- `--locale`: Tạo cho locale cụ thể
- `--all`: Tạo cho tất cả locales
- `--domain`: Domain (django hoặc djangojs)
- `--extension`: File extensions để scan
**Chức năng:**
- Tạo hoặc cập nhật .po files
- Scan code để tìm translatable strings
- Chuẩn bị cho quá trình dịch

### 28. **optimizemigration** - Tối Ưu Migration
**Vị trí:** `django/core/management/commands/optimizemigration.py`
**Cú pháp:**
```bash
python manage.py optimizemigration <app_name> <migration_name>
```
**Chức năng:**
- Tối ưu hóa migration operations
- Gộp các operations có thể gộp được
- Giảm số lượng operations trong migration

### 29. **squashmigrations** - Gộp Migrations
**Vị trí:** `django/core/management/commands/squashmigrations.py`
**Cú pháp:**
```bash
python manage.py squashmigrations <app_name> <start_migration> <end_migration>
```
**Chức năng:**
- Gộp nhiều migrations thành một
- Giảm số lượng migration files
- Tối ưu hóa migration history

---

## 🏗️ CẤU TRÚC FILE VÀ THƯ MỤC CHI TIẾT

### 1. **File apps.py** - Cấu Hình App
**Vị trí:** Trong mỗi Django app
**Mục đích:** Cấu hình metadata cho app
**Cấu trúc:**
```python
from django.apps import AppConfig

class MyAppConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'myapp'
    verbose_name = 'My Application'

    def ready(self):
        # Code chạy khi app ready
        import myapp.signals
```

### 2. **File __init__.py** - Package Initialization
**Vị trí:** Trong mỗi Python package
**Mục đích:** Đánh dấu thư mục là Python package
**Có thể chứa:**
- Import statements
- Package-level variables
- Initialization code

### 3. **File tests.py** - Unit Tests
**Vị trí:** Trong mỗi Django app
**Mục đích:** Chứa unit tests cho app
**Cấu trúc cơ bản:**
```python
from django.test import TestCase
from django.contrib.auth.models import User

class MyModelTest(TestCase):
    def setUp(self):
        # Setup test data
        pass

    def test_model_creation(self):
        # Test logic
        self.assertEqual(1, 1)
```

### 4. **Thư mục migrations/** - Database Migrations
**Vị trí:** Trong mỗi Django app
**Mục đích:** Chứa database migration files
**Cấu trúc:**
```
migrations/
├── __init__.py
├── 0001_initial.py
├── 0002_add_field.py
└── 0003_remove_field.py
```

### 5. **Thư mục templates/** - HTML Templates
**Vị trí:** Trong app hoặc project root
**Mục đích:** Chứa HTML templates
**Cấu trúc khuyến nghị:**
```
templates/
├── base.html
├── myapp/
│   ├── index.html
│   ├── detail.html
│   └── form.html
└── registration/
    ├── login.html
    └── signup.html
```

### 6. **Thư mục static/** - Static Files
**Vị trí:** Trong app hoặc project root
**Mục đích:** Chứa CSS, JS, images
**Cấu trúc:**
```
static/
├── css/
│   └── style.css
├── js/
│   └── script.js
├── images/
│   └── logo.png
└── myapp/
    ├── css/
    ├── js/
    └── images/
```

### 7. **File wsgi.py** - WSGI Configuration
**Vị trí:** Trong project root
**Mục đích:** Cấu hình WSGI cho production
**Nội dung:**
```python
import os
from django.core.wsgi import get_wsgi_application

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
application = get_wsgi_application()
```

### 8. **File asgi.py** - ASGI Configuration
**Vị trí:** Trong project root
**Mục đích:** Cấu hình ASGI cho async/WebSocket
**Nội dung:**
```python
import os
from django.core.asgi import get_asgi_application

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
application = get_asgi_application()
```

---

## 📊 CÁC FILE CẤU HÌNH NÂNG CAO

### 1. **requirements.txt** - Python Dependencies
**Vị trí:** Project root
**Mục đích:** Liệt kê các package Python cần thiết
**Cấu trúc:**
```
Django>=4.2.0
psycopg2-binary>=2.9.0
Pillow>=9.0.0
django-crispy-forms>=1.14.0
celery>=5.2.0
redis>=4.3.0
```

### 2. **Dockerfile** - Container Configuration
**Vị trí:** Project root
**Mục đích:** Cấu hình Docker container
**Cấu trúc cơ bản:**
```dockerfile
FROM python:3.11
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
```

### 3. **docker-compose.yml** - Multi-container Setup
**Vị trí:** Project root
**Mục đích:** Cấu hình nhiều services (web, db, redis)
**Cấu trúc:**
```yaml
version: '3.8'
services:
  web:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - db
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: myproject
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
```

### 4. **.env** - Environment Variables
**Vị trí:** Project root
**Mục đích:** Chứa biến môi trường nhạy cảm
**Nội dung:**
```
DEBUG=True
SECRET_KEY=your-secret-key-here
DATABASE_URL=postgresql://user:pass@localhost/dbname
REDIS_URL=redis://localhost:6379/0
```

---

## 🎯 DJANGO MODELS - CHI TIẾT FIELDS VÀ OPTIONS

### 1. **Model Fields Phổ Biến**
```python
from django.db import models

class MyModel(models.Model):
    # Text fields
    char_field = models.CharField(max_length=100)
    text_field = models.TextField()
    slug_field = models.SlugField(max_length=100)
    email_field = models.EmailField()
    url_field = models.URLField()

    # Number fields
    integer_field = models.IntegerField()
    big_integer_field = models.BigIntegerField()
    small_integer_field = models.SmallIntegerField()
    positive_integer_field = models.PositiveIntegerField()
    float_field = models.FloatField()
    decimal_field = models.DecimalField(max_digits=10, decimal_places=2)

    # Date/Time fields
    date_field = models.DateField()
    time_field = models.TimeField()
    datetime_field = models.DateTimeField()
    duration_field = models.DurationField()

    # Boolean fields
    boolean_field = models.BooleanField()
    null_boolean_field = models.BooleanField(null=True, blank=True)

    # File fields
    file_field = models.FileField(upload_to='files/')
    image_field = models.ImageField(upload_to='images/')

    # Relationship fields
    foreign_key = models.ForeignKey('OtherModel', on_delete=models.CASCADE)
    many_to_many = models.ManyToManyField('OtherModel')
    one_to_one = models.OneToOneField('OtherModel', on_delete=models.CASCADE)

    # Special fields
    json_field = models.JSONField()
    uuid_field = models.UUIDField()
    binary_field = models.BinaryField()
```

### 2. **Field Options Quan Trọng**
```python
class MyModel(models.Model):
    name = models.CharField(
        max_length=100,
        null=False,           # Cho phép NULL trong DB
        blank=False,          # Cho phép empty trong forms
        default='',           # Giá trị mặc định
        unique=True,          # Unique constraint
        db_index=True,        # Tạo database index
        verbose_name='Tên',   # Tên hiển thị
        help_text='Nhập tên', # Text hướng dẫn
        choices=[             # Lựa chọn có sẵn
            ('option1', 'Option 1'),
            ('option2', 'Option 2'),
        ],
        validators=[          # Custom validators
            validate_name,
        ]
    )
```

### 3. **Meta Options Cho Models**
```python
class MyModel(models.Model):
    name = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        # Tên bảng trong database
        db_table = 'my_custom_table'

        # Tên hiển thị
        verbose_name = 'My Model'
        verbose_name_plural = 'My Models'

        # Sắp xếp mặc định
        ordering = ['-created_at', 'name']

        # Unique constraints
        unique_together = [['field1', 'field2']]

        # Indexes
        indexes = [
            models.Index(fields=['name', 'created_at']),
        ]

        # Permissions
        permissions = [
            ('can_publish', 'Can publish articles'),
        ]

        # Abstract model
        abstract = True

        # Proxy model
        proxy = True
```

---

## 🎨 DJANGO VIEWS - CÁC LOẠI VIEWS CHI TIẾT

### 1. **Function-Based Views (FBV)**
```python
from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods

# Basic view
def my_view(request):
    context = {'data': 'Hello World'}
    return render(request, 'template.html', context)

# View với parameters
def detail_view(request, pk):
    obj = get_object_or_404(MyModel, pk=pk)
    return render(request, 'detail.html', {'object': obj})

# JSON response
def api_view(request):
    data = {'status': 'success', 'data': []}
    return JsonResponse(data)

# Require login
@login_required
def protected_view(request):
    return render(request, 'protected.html')

# Restrict HTTP methods
@require_http_methods(["GET", "POST"])
def form_view(request):
    if request.method == 'POST':
        # Process form
        return redirect('success')
    return render(request, 'form.html')
```

### 2. **Class-Based Views (CBV)**
```python
from django.views.generic import (
    ListView, DetailView, CreateView, UpdateView, DeleteView,
    TemplateView, RedirectView
)
from django.contrib.auth.mixins import LoginRequiredMixin

# Template view
class HomeView(TemplateView):
    template_name = 'home.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['extra_data'] = 'Hello'
        return context

# List view
class MyModelListView(ListView):
    model = MyModel
    template_name = 'mymodel_list.html'
    context_object_name = 'objects'
    paginate_by = 10

    def get_queryset(self):
        return MyModel.objects.filter(active=True)

# Detail view
class MyModelDetailView(DetailView):
    model = MyModel
    template_name = 'mymodel_detail.html'
    context_object_name = 'object'

# Create view
class MyModelCreateView(LoginRequiredMixin, CreateView):
    model = MyModel
    fields = ['name', 'description']
    template_name = 'mymodel_form.html'
    success_url = '/success/'

    def form_valid(self, form):
        form.instance.user = self.request.user
        return super().form_valid(form)

# Update view
class MyModelUpdateView(UpdateView):
    model = MyModel
    fields = ['name', 'description']
    template_name = 'mymodel_form.html'

# Delete view
class MyModelDeleteView(DeleteView):
    model = MyModel
    template_name = 'mymodel_confirm_delete.html'
    success_url = '/list/'
```

---

## 📝 DJANGO FORMS - CHI TIẾT VÀ VÍ DỤ

### 1. **Django Forms Cơ Bản**
```python
from django import forms
from django.contrib.auth.models import User

class ContactForm(forms.Form):
    name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Nhập tên của bạn'
        })
    )
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-control'
        })
    )
    message = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 5
        })
    )

    def clean_email(self):
        email = self.cleaned_data['email']
        if User.objects.filter(email=email).exists():
            raise forms.ValidationError('Email đã tồn tại')
        return email
```

### 2. **ModelForm - Form Từ Model**
```python
from django.forms import ModelForm

class MyModelForm(ModelForm):
    class Meta:
        model = MyModel
        fields = ['name', 'description', 'category']
        # hoặc exclude = ['created_at', 'updated_at']

        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'category': forms.Select(attrs={'class': 'form-control'}),
        }

        labels = {
            'name': 'Tên',
            'description': 'Mô tả',
            'category': 'Danh mục',
        }

        help_texts = {
            'name': 'Nhập tên cho item',
        }

    def clean_name(self):
        name = self.cleaned_data['name']
        if len(name) < 3:
            raise forms.ValidationError('Tên phải có ít nhất 3 ký tự')
        return name
```

### 3. **Form Widgets Chi Tiết**
```python
class AdvancedForm(forms.Form):
    # Text inputs
    text_input = forms.CharField(widget=forms.TextInput)
    password_input = forms.CharField(widget=forms.PasswordInput)
    hidden_input = forms.CharField(widget=forms.HiddenInput)

    # Textarea
    textarea = forms.CharField(widget=forms.Textarea(attrs={'rows': 4, 'cols': 40}))

    # Select widgets
    choice_field = forms.ChoiceField(
        choices=[('1', 'Option 1'), ('2', 'Option 2')],
        widget=forms.Select
    )
    multiple_choice = forms.MultipleChoiceField(
        choices=[('1', 'Option 1'), ('2', 'Option 2')],
        widget=forms.CheckboxSelectMultiple
    )

    # Date/Time widgets
    date_field = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}))
    datetime_field = forms.DateTimeField(widget=forms.DateTimeInput(attrs={'type': 'datetime-local'}))

    # File widgets
    file_field = forms.FileField(widget=forms.FileInput)
    image_field = forms.ImageField(widget=forms.FileInput(attrs={'accept': 'image/*'}))

    # Boolean widgets
    boolean_field = forms.BooleanField(widget=forms.CheckboxInput)

    # Custom widget attributes
    styled_field = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control custom-class',
            'data-toggle': 'tooltip',
            'title': 'Tooltip text',
            'placeholder': 'Enter text here'
        })
    )
```

---

## 🔐 DJANGO ADMIN - CẤU HÌNH CHI TIẾT

### 1. **Admin Configuration Cơ Bản**
```python
from django.contrib import admin
from .models import MyModel

@admin.register(MyModel)
class MyModelAdmin(admin.ModelAdmin):
    # Hiển thị trong list
    list_display = ['name', 'created_at', 'is_active']
    list_filter = ['is_active', 'created_at', 'category']
    search_fields = ['name', 'description']
    list_per_page = 25

    # Sắp xếp
    ordering = ['-created_at']

    # Fields trong form
    fields = ['name', 'description', 'category', 'is_active']
    # hoặc exclude = ['created_at', 'updated_at']

    # Readonly fields
    readonly_fields = ['created_at', 'updated_at']

    # Fieldsets (nhóm fields)
    fieldsets = (
        ('Thông tin cơ bản', {
            'fields': ('name', 'description')
        }),
        ('Cài đặt', {
            'fields': ('category', 'is_active'),
            'classes': ('collapse',)  # Có thể thu gọn
        }),
        ('Thời gian', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    # Actions
    actions = ['make_active', 'make_inactive']

    def make_active(self, request, queryset):
        queryset.update(is_active=True)
    make_active.short_description = "Kích hoạt các items đã chọn"

    def make_inactive(self, request, queryset):
        queryset.update(is_active=False)
    make_inactive.short_description = "Vô hiệu hóa các items đã chọn"
```

### 2. **Inline Admin**
```python
class RelatedModelInline(admin.TabularInline):
    model = RelatedModel
    extra = 1  # Số form trống hiển thị
    max_num = 10  # Số form tối đa

class MyModelAdmin(admin.ModelAdmin):
    inlines = [RelatedModelInline]
```

### 3. **Custom Admin Methods**
```python
class MyModelAdmin(admin.ModelAdmin):
    list_display = ['name', 'get_category_name', 'colored_status']

    def get_category_name(self, obj):
        return obj.category.name if obj.category else 'No Category'
    get_category_name.short_description = 'Category'
    get_category_name.admin_order_field = 'category__name'

    def colored_status(self, obj):
        if obj.is_active:
            return format_html('<span style="color: green;">Active</span>')
        return format_html('<span style="color: red;">Inactive</span>')
    colored_status.short_description = 'Status'
```

---

## 🌐 DJANGO URLS - ROUTING CHI TIẾT

### 1. **URL Patterns Cơ Bản**
```python
from django.urls import path, re_path, include
from . import views

# App-level urls.py
app_name = 'myapp'

urlpatterns = [
    # Basic path
    path('', views.index, name='index'),
    path('about/', views.about, name='about'),

    # Path với parameters
    path('post/<int:pk>/', views.post_detail, name='post_detail'),
    path('category/<slug:slug>/', views.category_view, name='category'),
    path('user/<str:username>/', views.user_profile, name='user_profile'),

    # Path với multiple parameters
    path('post/<int:year>/<int:month>/<slug:slug>/', views.post_archive, name='post_archive'),

    # Regex path
    re_path(r'^articles/(?P<year>[0-9]{4})/$', views.year_archive, name='year_archive'),

    # Include other URL configs
    path('api/', include('myapp.api.urls')),

    # Class-based views
    path('list/', views.MyModelListView.as_view(), name='list'),
    path('create/', views.MyModelCreateView.as_view(), name='create'),
    path('update/<int:pk>/', views.MyModelUpdateView.as_view(), name='update'),
    path('delete/<int:pk>/', views.MyModelDeleteView.as_view(), name='delete'),
]
```

### 2. **URL Converters**
```python
# Built-in converters
path('int/<int:value>/', views.int_view)        # Số nguyên
path('slug/<slug:value>/', views.slug_view)     # Slug (a-z, 0-9, -, _)
path('str/<str:value>/', views.str_view)        # String (không chứa /)
path('path/<path:value>/', views.path_view)     # String (có thể chứa /)
path('uuid/<uuid:value>/', views.uuid_view)     # UUID

# Custom converter
class YearConverter:
    regex = '[0-9]{4}'

    def to_python(self, value):
        return int(value)

    def to_url(self, value):
        return '%04d' % value

# Đăng ký converter
from django.urls import register_converter
register_converter(YearConverter, 'yyyy')

# Sử dụng
path('year/<yyyy:year>/', views.year_view)
```

### 3. **URL Namespacing**
```python
# Project urls.py
urlpatterns = [
    path('admin/', admin.site.urls),
    path('blog/', include('blog.urls', namespace='blog')),
    path('shop/', include('shop.urls', namespace='shop')),
]

# App urls.py
app_name = 'blog'
urlpatterns = [
    path('', views.index, name='index'),  # blog:index
    path('post/<int:pk>/', views.detail, name='detail'),  # blog:detail
]

# Trong template
{% url 'blog:index' %}
{% url 'blog:detail' pk=post.pk %}

# Trong views
from django.urls import reverse
url = reverse('blog:detail', kwargs={'pk': 1})
```

---

## 🎨 DJANGO TEMPLATES - SYNTAX VÀ TAGS

### 1. **Template Variables và Filters**
```html
<!-- Variables -->
{{ variable }}
{{ object.attribute }}
{{ object.method }}
{{ list.0 }}

<!-- Filters -->
{{ name|lower }}
{{ text|truncatewords:30 }}
{{ date|date:"Y-m-d" }}
{{ price|floatformat:2 }}
{{ content|safe }}
{{ text|default:"No content" }}
{{ list|length }}
{{ text|capfirst }}
{{ url|urlencode }}

<!-- Chaining filters -->
{{ text|lower|capfirst }}
{{ date|date:"Y-m-d"|default:"No date" }}
```

### 2. **Template Tags Quan Trọng**
```html
<!-- Control flow -->
{% if condition %}
    Content if true
{% elif other_condition %}
    Content if other condition
{% else %}
    Content if false
{% endif %}

{% for item in list %}
    {{ item }}
    {% empty %}
    No items found
{% endfor %}

<!-- Loop variables -->
{% for item in list %}
    {{ forloop.counter }}     <!-- 1, 2, 3... -->
    {{ forloop.counter0 }}    <!-- 0, 1, 2... -->
    {{ forloop.first }}       <!-- True for first iteration -->
    {{ forloop.last }}        <!-- True for last iteration -->
    {{ forloop.revcounter }}  <!-- Countdown -->
{% endfor %}

<!-- URL generation -->
{% url 'view_name' %}
{% url 'view_name' arg1 arg2 %}
{% url 'view_name' pk=object.pk %}
{% url 'namespace:view_name' %}

<!-- Static files -->
{% load static %}
{% static 'css/style.css' %}
{% static 'js/script.js' %}

<!-- Template inheritance -->
{% extends 'base.html' %}
{% block content %}
    Block content here
{% endblock %}

<!-- Include templates -->
{% include 'partial.html' %}
{% include 'partial.html' with variable=value %}

<!-- Comments -->
{# Single line comment #}
{% comment %}
Multi-line comment
{% endcomment %}

<!-- CSRF protection -->
{% csrf_token %}

<!-- Load template tags -->
{% load custom_tags %}
{% load humanize %}
{% load i18n %}
```

### 3. **Template Inheritance**
```html
<!-- base.html -->
<!DOCTYPE html>
<html>
<head>
    <title>{% block title %}Default Title{% endblock %}</title>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <header>
        {% block header %}
        <h1>My Site</h1>
        {% endblock %}
    </header>

    <main>
        {% block content %}{% endblock %}
    </main>

    <footer>
        {% block footer %}
        <p>&copy; 2024 My Site</p>
        {% endblock %}
    </footer>

    {% block extra_js %}{% endblock %}
</body>
</html>

<!-- child.html -->
{% extends 'base.html' %}

{% block title %}Page Title{% endblock %}

{% block content %}
<h2>Page Content</h2>
<p>This is the page content.</p>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/page.css' %}">
{% endblock %}
```

---

## 🔧 DJANGO SETTINGS - CẤU HÌNH CHI TIẾT

### 1. **Database Settings**
```python
# SQLite (development)
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# PostgreSQL (production)
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'mydatabase',
        'USER': 'mydatabaseuser',
        'PASSWORD': 'mypassword',
        'HOST': '127.0.0.1',
        'PORT': '5432',
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}

# Multiple databases
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'main_db',
        'USER': 'user',
        'PASSWORD': 'pass',
        'HOST': 'localhost',
        'PORT': '5432',
    },
    'users_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'users_db',
        'USER': 'mysql_user',
        'PASSWORD': 'mysql_pass',
        'HOST': 'localhost',
        'PORT': '3306',
    }
}
```

### 2. **Static Files Settings**
```python
# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'  # Production
STATICFILES_DIRS = [
    BASE_DIR / 'static',  # Development
    BASE_DIR / 'assets',
]

STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# Media files (user uploads)
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'
```

### 3. **Email Settings**
```python
# Console backend (development)
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# SMTP backend (production)
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-password'
DEFAULT_FROM_EMAIL = '<EMAIL>'

# File backend (testing)
EMAIL_BACKEND = 'django.core.mail.backends.filebased.EmailBackend'
EMAIL_FILE_PATH = BASE_DIR / 'sent_emails'
```

### 4. **Cache Settings**
```python
# Dummy cache (development)
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# Redis cache (production)
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Database cache
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
        'LOCATION': 'my_cache_table',
    }
}
```

### 5. **Security Settings**
```python
# Security settings for production
DEBUG = False
ALLOWED_HOSTS = ['yourdomain.com', 'www.yourdomain.com']

# HTTPS settings
SECURE_SSL_REDIRECT = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Cookie security
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# Content Security Policy
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'
```

---

## 📚 TỔNG KẾT VÀ TIPS SỬ DỤNG

### 🎯 **Workflow Phát Triển Django Chuẩn:**

1. **Khởi tạo dự án:**
   ```bash
   django-admin startproject myproject
   cd myproject
   python manage.py startapp myapp
   ```

2. **Cấu hình cơ bản:**
   - Thêm app vào `INSTALLED_APPS`
   - Cấu hình database trong `settings.py`
   - Tạo `requirements.txt`

3. **Phát triển models:**
   ```bash
   # Sau khi tạo/sửa models
   python manage.py makemigrations
   python manage.py migrate
   ```

4. **Tạo superuser:**
   ```bash
   python manage.py createsuperuser
   ```

5. **Chạy server phát triển:**
   ```bash
   python manage.py runserver
   ```

6. **Testing:**
   ```bash
   python manage.py test
   python manage.py test myapp
   ```

7. **Production deployment:**
   ```bash
   python manage.py collectstatic
   python manage.py check --deploy
   ```

### 🔍 **Debug và Troubleshooting:**

- `python manage.py check` - Kiểm tra lỗi cấu hình
- `python manage.py showmigrations` - Xem trạng thái migrations
- `python manage.py dbshell` - Truy cập database trực tiếp
- `python manage.py shell` - Python shell với Django context
- `python manage.py diffsettings` - So sánh settings

### 📖 **Tài liệu tham khảo:**
- Django Documentation: https://docs.djangoproject.com/
- Django REST Framework: https://www.django-rest-framework.org/
- Django Packages: https://djangopackages.org/

---

**🎉 HOÀN THÀNH BẢNG TRA CỨU DJANGO CHI TIẾT**

*Bảng tra cứu này bao gồm tất cả các lệnh, file cấu hình, và thành phần quan trọng của Django với mô tả chi tiết bằng tiếng Việt. Hãy bookmark và sử dụng như một từ điển code Django hoàn chỉnh cho dự án của bạn!*
